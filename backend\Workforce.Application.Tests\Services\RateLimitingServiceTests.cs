using FluentAssertions;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Moq;
using Workforce.Application.Services;

namespace Workforce.Application.Tests.Services
{
    public class RateLimitingServiceTests : IDisposable
    {
        private readonly IMemoryCache _memoryCache;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly RateLimitingService _rateLimitingService;

        public RateLimitingServiceTests()
        {
            _memoryCache = new MemoryCache(new MemoryCacheOptions());
            _mockConfiguration = new Mock<IConfiguration>();

            // Setup default configuration values
            _mockConfiguration.Setup(x => x["SecuritySettings:RateLimit:IpMaxAttempts"]).Returns("3");
            _mockConfiguration.Setup(x => x["SecuritySettings:RateLimit:EmailMaxAttempts"]).Returns("2");
            _mockConfiguration.Setup(x => x["SecuritySettings:RateLimit:IpWindowMinutes"]).Returns("1");
            _mockConfiguration.Setup(x => x["SecuritySettings:RateLimit:EmailWindowMinutes"]).Returns("1");

            _rateLimitingService = new RateLimitingService(_memoryCache, _mockConfiguration.Object);
        }

        [Fact]
        public async Task IsIpRateLimitExceededAsync_WithNoAttempts_ShouldReturnFalse()
        {
            // Arrange
            var ipAddress = "***********";

            // Act
            var result = await _rateLimitingService.IsIpRateLimitExceededAsync(ipAddress);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task IsIpRateLimitExceededAsync_WithAttemptsUnderLimit_ShouldReturnFalse()
        {
            // Arrange
            var ipAddress = "***********";

            // Act
            await _rateLimitingService.RecordIpLoginAttemptAsync(ipAddress);
            await _rateLimitingService.RecordIpLoginAttemptAsync(ipAddress);
            var result = await _rateLimitingService.IsIpRateLimitExceededAsync(ipAddress);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task IsIpRateLimitExceededAsync_WithAttemptsAtLimit_ShouldReturnTrue()
        {
            // Arrange
            var ipAddress = "***********";

            // Act
            await _rateLimitingService.RecordIpLoginAttemptAsync(ipAddress);
            await _rateLimitingService.RecordIpLoginAttemptAsync(ipAddress);
            await _rateLimitingService.RecordIpLoginAttemptAsync(ipAddress);
            var result = await _rateLimitingService.IsIpRateLimitExceededAsync(ipAddress);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task IsEmailRateLimitExceededAsync_WithNoAttempts_ShouldReturnFalse()
        {
            // Arrange
            var email = "<EMAIL>";

            // Act
            var result = await _rateLimitingService.IsEmailRateLimitExceededAsync(email);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task IsEmailRateLimitExceededAsync_WithAttemptsUnderLimit_ShouldReturnFalse()
        {
            // Arrange
            var email = "<EMAIL>";

            // Act
            await _rateLimitingService.RecordEmailLoginAttemptAsync(email);
            var result = await _rateLimitingService.IsEmailRateLimitExceededAsync(email);

            // Assert
            result.Should().BeFalse();
        }

        [Fact]
        public async Task IsEmailRateLimitExceededAsync_WithAttemptsAtLimit_ShouldReturnTrue()
        {
            // Arrange
            var email = "<EMAIL>";

            // Act
            await _rateLimitingService.RecordEmailLoginAttemptAsync(email);
            await _rateLimitingService.RecordEmailLoginAttemptAsync(email);
            var result = await _rateLimitingService.IsEmailRateLimitExceededAsync(email);

            // Assert
            result.Should().BeTrue();
        }

        [Fact]
        public async Task GetIpRateLimitResetTimeAsync_WithNoAttempts_ShouldReturnZero()
        {
            // Arrange
            var ipAddress = "***********";

            // Act
            var result = await _rateLimitingService.GetIpRateLimitResetTimeAsync(ipAddress);

            // Assert
            result.Should().Be(TimeSpan.Zero);
        }

        [Fact]
        public async Task GetEmailRateLimitResetTimeAsync_WithNoAttempts_ShouldReturnZero()
        {
            // Arrange
            var email = "<EMAIL>";

            // Act
            var result = await _rateLimitingService.GetEmailRateLimitResetTimeAsync(email);

            // Assert
            result.Should().Be(TimeSpan.Zero);
        }

        [Fact]
        public async Task GetIpRateLimitResetTimeAsync_WithAttempts_ShouldReturnPositiveTime()
        {
            // Arrange
            var ipAddress = "***********";

            // Act
            await _rateLimitingService.RecordIpLoginAttemptAsync(ipAddress);
            var result = await _rateLimitingService.GetIpRateLimitResetTimeAsync(ipAddress);

            // Assert
            result.Should().BeGreaterThan(TimeSpan.Zero);
            result.Should().BeLessThanOrEqualTo(TimeSpan.FromMinutes(1));
        }

        [Fact]
        public async Task GetEmailRateLimitResetTimeAsync_WithAttempts_ShouldReturnPositiveTime()
        {
            // Arrange
            var email = "<EMAIL>";

            // Act
            await _rateLimitingService.RecordEmailLoginAttemptAsync(email);
            var result = await _rateLimitingService.GetEmailRateLimitResetTimeAsync(email);

            // Assert
            result.Should().BeGreaterThan(TimeSpan.Zero);
            result.Should().BeLessThanOrEqualTo(TimeSpan.FromMinutes(1));
        }

        [Fact]
        public async Task ConcurrentRecordIpLoginAttemptAsync_ShouldHandleRaceConditionCorrectly()
        {
            // Arrange
            var ipAddress = "***********";
            var numberOfConcurrentTasks = 10;

            // Act - Execute multiple concurrent attempts
            var tasks = Enumerable.Range(0, numberOfConcurrentTasks)
                .Select(_ => _rateLimitingService.RecordIpLoginAttemptAsync(ipAddress))
                .ToArray();

            await Task.WhenAll(tasks);

            // Assert - Check that the rate limit correctly reflects all attempts
            var isLimited = await _rateLimitingService.IsIpRateLimitExceededAsync(ipAddress);
            isLimited.Should().BeTrue("because we recorded more attempts than the limit of 3");
        }

        [Fact]
        public async Task ConcurrentRecordEmailLoginAttemptAsync_ShouldHandleRaceConditionCorrectly()
        {
            // Arrange
            var email = "<EMAIL>";
            var numberOfConcurrentTasks = 10;

            // Act - Execute multiple concurrent attempts
            var tasks = Enumerable.Range(0, numberOfConcurrentTasks)
                .Select(_ => _rateLimitingService.RecordEmailLoginAttemptAsync(email))
                .ToArray();

            await Task.WhenAll(tasks);

            // Assert - Check that the rate limit correctly reflects all attempts
            var isLimited = await _rateLimitingService.IsEmailRateLimitExceededAsync(email);
            isLimited.Should().BeTrue("because we recorded more attempts than the limit of 2");
        }

        [Fact]
        public async Task MixedConcurrentOperations_ShouldMaintainDataIntegrity()
        {
            // Arrange
            var ipAddress = "***********";
            var email = "<EMAIL>";

            // Act - Execute mixed concurrent operations
            var tasks = new List<Task>();

            // Add concurrent record operations
            for (int i = 0; i < 5; i++)
            {
                tasks.Add(_rateLimitingService.RecordIpLoginAttemptAsync(ipAddress));
                tasks.Add(_rateLimitingService.RecordEmailLoginAttemptAsync(email));
            }

            // Add concurrent check operations
            for (int i = 0; i < 3; i++)
            {
                tasks.Add(_rateLimitingService.IsIpRateLimitExceededAsync(ipAddress));
                tasks.Add(_rateLimitingService.IsEmailRateLimitExceededAsync(email));
                tasks.Add(_rateLimitingService.GetIpRateLimitResetTimeAsync(ipAddress));
                tasks.Add(_rateLimitingService.GetEmailRateLimitResetTimeAsync(email));
            }

            await Task.WhenAll(tasks);

            // Assert - Verify final state is consistent
            var ipLimited = await _rateLimitingService.IsIpRateLimitExceededAsync(ipAddress);
            var emailLimited = await _rateLimitingService.IsEmailRateLimitExceededAsync(email);

            ipLimited.Should().BeTrue("because we recorded 5 attempts which exceeds the limit of 3");
            emailLimited.Should().BeTrue("because we recorded 5 attempts which exceeds the limit of 2");
        }

        [Fact]
        public async Task EmailCaseInsensitive_ShouldTreatSameEmailConsistently()
        {
            // Arrange
            var email1 = "<EMAIL>";
            var email2 = "<EMAIL>";
            var email3 = "<EMAIL>";

            // Act
            await _rateLimitingService.RecordEmailLoginAttemptAsync(email1);
            await _rateLimitingService.RecordEmailLoginAttemptAsync(email2);
            var result = await _rateLimitingService.IsEmailRateLimitExceededAsync(email3);

            // Assert
            result.Should().BeTrue("because all email variations should be treated as the same email");
        }

        public void Dispose()
        {
            _rateLimitingService?.Dispose();
            _memoryCache?.Dispose();
        }
    }
}
