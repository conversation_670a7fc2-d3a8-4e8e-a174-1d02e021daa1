using FluentValidation;
using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;
using Workforce.Application.Options;
using Workforce.Application.Services;

namespace Workforce.Application
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddApplication(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddAutoMapper(Assembly.GetExecutingAssembly());
            services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));
            services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());

            // Configure options
            services.Configure<AccountLockoutOptions>(options =>
                configuration.GetSection("SecuritySettings:AccountLockout").Bind(options));

            // Register application services
            services.AddScoped<IPasswordHashingService, PasswordHashingService>();
            services.AddScoped<IJwtTokenService, JwtTokenService>();
            services.AddScoped<IAuditLoggingService, AuditLoggingService>();
            services.AddScoped<IRateLimitingService, RateLimitingService>();

            // Add memory cache for rate limiting
            services.AddMemoryCache();

            return services;
        }
    }
}
