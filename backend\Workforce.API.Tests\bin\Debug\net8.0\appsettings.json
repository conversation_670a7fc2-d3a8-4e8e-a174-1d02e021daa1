{"DatabaseSettings": {"Host": "localhost", "Port": 5432, "Database": "hirenow", "Username": "postgres", "Password": "postgres"}, "JwtSettings": {"SecretKey": "your-super-secret-jwt-key-that-is-at-least-32-characters-long-for-security", "Issuer": "HireNow.API", "Audience": "HireNow.Client", "ExpirationMinutes": 60}, "SecuritySettings": {"AccountLockout": {"MaxFailedAttempts": "${ACCOUNT_LOCKOUT_MAX_ATTEMPTS:5}", "LockoutDurationMinutes": "${ACCOUNT_LOCKOUT_DURATION:15}"}, "RateLimit": {"IpMaxAttempts": "${RATE_LIMIT_IP_MAX_ATTEMPTS:10}", "IpWindowMinutes": "${RATE_LIMIT_IP_WINDOW_MINUTES:1}", "EmailMaxAttempts": "${RATE_LIMIT_EMAIL_MAX_ATTEMPTS:5}", "EmailWindowMinutes": "${RATE_LIMIT_EMAIL_WINDOW_MINUTES:1}"}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}